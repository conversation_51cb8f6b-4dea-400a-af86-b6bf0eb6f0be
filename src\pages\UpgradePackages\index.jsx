import { useState, useEffect } from "react";
import {
  Card,
  Button,
  Space,
  Table,
  Tag,
  Breadcrumb,
  Input,
  Select,
  DatePicker,
  Statistic,
  Modal,
  Form,
  Upload,
  Radio,
  message,
  Tooltip,
  Row,
  Col,
  Descriptions,
  Drawer,
  Divider,
} from "antd";
import {
  PlusOutlined,
  UploadOutlined,
  DownloadOutlined,
  ReloadOutlined,
  SearchOutlined,
  CloudUploadOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
} from "@ant-design/icons";
import {
  getFirmwarePackageList,
  createFirmwarePackage,
  getFirmwarePackageStatus,
  delFirmwarePackage,
  updateFirmwarePackage,
  downloadFirmwarePackage,
  approveFirmwarePackage,
  uploadQsqFirmwarePackage,
  getFirmwarePackagebyId,
} from "../../service/upgradePackages";
import { dictionaryConfigApi } from "../../service/DictionaryConfig";
import AdvancedSearch from "../../components/advancedSearch";
import "./styles.css";
import { set } from "lodash";

const { RangePicker } = DatePicker;

const UpgradePackages = () => {
  // const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [approvalModalVisible, setApprovalModalVisible] = useState(false);
  const [packageToDelete, setPackageToDelete] = useState(null);
  const [packageToEdit, setPackageToEdit] = useState(null);
  const [packageToApprove, setPackageToApprove] = useState(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [searchForm] = Form.useForm();
  const [form] = Form.useForm();
  const [approvalForm] = Form.useForm();
  const [firmwarePackageList, setFirmwarePackageList] = useState([]);
  const [total, setTotal] = useState(0);
  const [statusData, setStatusData] = useState({});
  const [deviceTypeDict, setDeviceTypeDict] = useState([]);
  const [advancedSearchVisible, setAdvancedSearchVisible] = useState(false);
  const [upDataPackage, setUpDataPackage] = useState(null);
  const [detailsModalVisible, setDetailsModalVisible] = useState(false);
  const [loading,setLoading] = useState(false)

  const [searchParams, setSearchParams] = useState({
    page: 0,
    size: 10,
    packageType: "",
    status: "",
    startDate: "",
    endDate: "",
  });

  // 获取列表
  const fetchFirmwarePackageList = async () => {
    setLoading(true)
    try {
      const res = await getFirmwarePackageList(searchParams);
      setFirmwarePackageList(res.content);
      setTotal(res.totalElements);
      setLoading(false)
    } catch (error) {
      console.error("获取升级包列表失败", error);
    }
  };

  // 获取更新包状态
  const fetchFirmwarePackageStatus = async () => {
    try {
      const res = await getFirmwarePackageStatus();
      setStatusData(res);
    } catch (error) {
      console.error("获取更新包状态失败", error);
    }
  };

  // 获取设备类型字典数据
  const fetchDeviceTypeDict = async () => {
    try {
      const res = await dictionaryConfigApi.getDictItemByType("device_version");
      setDeviceTypeDict(res);
    } catch (error) {
      console.error("获取设备类型字典数据失败", error);
    }
  };

  useEffect(() => {
    fetchFirmwarePackageList();
  }, [searchParams]);

  useEffect(() => {
    fetchFirmwarePackageStatus();
    fetchDeviceTypeDict();
    // 清理函数，在组件卸载时取消请求
    return () => {
      if (window.cancelToken) {
        window.cancelToken(); // 取消请求
      }
    };
  }, []);

  const handleSearch = (values) => {
    const [startDate, endDate] = values.dateRange || [];
    setSearchParams({
      ...searchParams,
      page: 0,
      packageType: values.packageType,
      status: values.status,
      startDate: startDate ? startDate.format("YYYY-MM-DD") : "",
      endDate: endDate ? endDate.format("YYYY-MM-DD") : "",
    });
  };

  const handleReset = () => {
    searchForm.resetFields();
    setSearchParams({
      page: 0,
      size: 10,
      packageType: "",
      status: "",
      startDate: "",
      endDate: "",
    });
  };

  const handleViewPackage = async (record) => {
    try {
      const res = await getFirmwarePackagebyId(record.packageId);
      setUpDataPackage(res);
      setDetailsModalVisible(true);
    } catch (error) {
      message.error("获取升级包详情失败：" + (error.message || "未知错误"));
    }
  };

  // Handle pagination change
  const handleTableChange = (pagination) => {
    setSearchParams({
      ...searchParams,
      page: pagination.current - 1,
      size: pagination.pageSize,
    });
  };

  // Mock data for statistics
  const statistics = [
    {
      title: "待审批升级包",
      value: statusData?.PENDING ?? 0,
      icon: <ClockCircleOutlined style={{ fontSize: 24, color: "#faad14" }} />,
    },
    {
      title: "已发布升级包",
      value: statusData?.APPROVED ?? 0,
      icon: <CloudUploadOutlined style={{ fontSize: 24, color: "#1890ff" }} />,
    },
    {
      title: "已拒绝升级包",
      value: statusData?.REJECTED ?? 0,
      icon: (
        <ExclamationCircleOutlined style={{ fontSize: 24, color: "#ff4d4f" }} />
      ),
    },
  ];

  // Table columns
  const columns = [
    {
      title: "升级包名称",
      dataIndex: "packageName",
      key: "packageName",
      ellipsis: true,
      render: (text, record) => {
        return <a onClick={() => handleViewPackage(record)}>{text}</a>;
      },
    },
    {
      title: "版本号",
      dataIndex: "version",
      key: "version",
      // width: 100,
      ellipsis: true,
    },
    {
      title: "类型",
      dataIndex: "packageType",
      key: "packageType",
      ellipsis: true,
      // width: 100,
      render: (type) => (type === "FIRMWARE" ? "驱鼠器固件" : "安全补丁"),
    },
    {
      title: "文件大小",
      dataIndex: "fileSize",
      key: "fileSize",
      // width: 100,
      ellipsis: true,
      render: (size) => `${(size / 1024 / 1024).toFixed(2)} MB`,
    },
    // {
    //   title: '发布日期',
    //   dataIndex: 'releaseDate',
    //   key: 'releaseDate',
    // },
    // {
    //   title: '状态',
    //   dataIndex: 'status',
    //   key: 'status',
    //   render: status => (
    //     <Tag
    //       color={
    //         status === 'APPROVED'
    //           ? 'success'
    //           : status === 'PENDING'
    //           ? 'warning'
    //           : 'error'
    //       }
    //     >
    //       {status === 'APPROVED'
    //         ? '已发布'
    //         : status === 'PENDING'
    //         ? '待审批'
    //         : '已拒绝'}
    //     </Tag>
    //   )
    // },
    {
      title: "兼容设备型号",
      dataIndex: "compatibleDevices",
      key: "compatibleDevices",
      ellipsis: true,
    },
    {
      title: "升级包描述",
      dataIndex: "description",
      key: "description",
      ellipsis: true,
      width: "20%",
    },
    {
      title: "备注",
      dataIndex: "remark",
      key: "remark",
      ellipsis: true,
      width: "20%",
    },
    {
      title: "上传时间",
      dataIndex: "createdAt",
      key: "createdAt",
      ellipsis: true,
    },
    {
      title: "操作",
      key: "action",
      ellipsis: true,
      render: (_, record) => (
        <Space>
          {/* {record.status === 'PENDING' && (
            <Tooltip title='审批'>
              <Button 
                type="text" 
                icon={<CheckCircleOutlined />} 
                onClick={() => {
                  setPackageToApprove(record);
                  setApprovalModalVisible(true);
                }}
              ></Button>
            </Tooltip>
          )} */}
          {/* <Tooltip title='编辑'>
            <Button
              type='text'
              icon={<EditOutlined />}
              onClick={() => handleEditPackage(record)}
            ></Button>
          </Tooltip> */}
          <Tooltip title="详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewPackage(record)}
            ></Button>
          </Tooltip>
          <Tooltip title="下载">
            <Button
              type="text"
              icon={<DownloadOutlined />}
              onClick={() => handleDownloadPackage(record)}
            ></Button>
          </Tooltip>
          <Tooltip title="删除">
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => {
                setPackageToDelete(record);
                setDeleteModalVisible(true);
              }}
            ></Button>
          </Tooltip>
        </Space>
      ),
    },
  ];

  // Handle edit package
  const handleEditPackage = async (record) => {
    setPackageToEdit(record);
    setIsEditMode(true);
    setCreateModalVisible(true);

    // 处理兼容设备型号数据，将逗号分隔的字符串转换为数组
    let compatibleDevicesArray = record.compatibleDevices;
    if (compatibleDevicesArray && typeof compatibleDevicesArray === "string") {
      compatibleDevicesArray = compatibleDevicesArray
        .split(",")
        .map((code) => code.trim());
    }

    // Set form values
    form.setFieldsValue({
      packageName: record.packageName,
      packageType: record.packageType,
      version: record.version,
      compatibleDevices: compatibleDevicesArray,
      description: record.description,
      remark: record.remark,
      lockVersion: record.lockVersion,
      // Note: We don't set the file field as it's not available in the record
    });
  };

  // Handle download package
  const handleDownloadPackage = async (record) => {
    try {
      const blob = await downloadFirmwarePackage(record.packageId);
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.download = record.downloadName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      message.success("下载成功");
    } catch (error) {
      message.error("获取下载链接失败：" + (error.message || "未知错误"));
      console.error("获取下载链接失败:", error);
    }
  };

  const handleCreatePackage = async (values) => {
    try {
      // 如果是驱鼠器固件类型，则直接调用uploadFirmwarePackage接口
      if (values.packageType === "FIRMWARE" && !isEditMode) {
        if (!values.file) {
          message.error("请上传升级包文件");
          return;
        }

        const file = values.file.fileList[0].originFileObj;
        try {
          await uploadQsqFirmwarePackage(file);
          message.success("驱鼠器固件上传成功！");
          await fetchFirmwarePackageStatus();

          setCreateModalVisible(false);
          form.resetFields();
          setIsEditMode(false);
          setPackageToEdit(null);
          // 刷新列表
          fetchFirmwarePackageList();
        } catch (error) {
          message.error("驱鼠器固件上传失败：" + (error.message || "未知错误"));
          console.error("驱鼠器固件上传失败:", error);
        }
        return;
      }

      // 其他类型或编辑模式仍使用原有逻辑
      const formData = new FormData();
      formData.append("packageName", values.packageName);
      formData.append("packageType", values.packageType);
      formData.append("version", values.version);

      if (values.compatibleDevices) {
        // 如果是数组，则转换为逗号分隔的字符串
        const compatibleDevicesStr = Array.isArray(values.compatibleDevices)
          ? values.compatibleDevices.join(",")
          : values.compatibleDevices;
        formData.append("compatibleDevices", compatibleDevicesStr);
      }

      if (values.description) {
        formData.append("description", values.description);
      }

      if (values.remark) {
        formData.append("remark", values.remark);
      }

      if (values.lockVersion !== null && values.lockVersion !== undefined) {
        formData.append("lockVersion", values.lockVersion);
      }

      if (values.file && values.file.fileList[0]) {
        formData.append("file", values.file.fileList[0].originFileObj);
      } else if (!isEditMode) {
        message.error("请上传升级包文件");
        return;
      }

      if (isEditMode) {
        // Update existing package
        await updateFirmwarePackage(packageToEdit.packageId, formData);
        message.success("升级包更新成功！");
      } else {
        // Create new package
        await createFirmwarePackage(formData);
        message.success("升级包创建成功！");
        await fetchFirmwarePackageStatus();
      }

      setCreateModalVisible(false);
      form.resetFields();
      setIsEditMode(false);
      setPackageToEdit(null);
      // Refresh the list
      fetchFirmwarePackageList();
    } catch (error) {
      // message.error(
      //   isEditMode
      //     ? '更新升级包失败：' + (error.message || '未知错误')
      //     : '创建升级包失败：' + (error.message || '未知错误')
      // )
      console.error(isEditMode ? "更新升级包失败:" : "创建升级包失败:", error);
    }
  };

  // Handle approve/reject package
  // const handleApprovePackage = async (values) => {
  //   if (!packageToApprove) return;

  //   try {
  //      await approveFirmwarePackage(packageToApprove.packageId, values.approvalStatus);
  //     message.success(`升级包已${values.approvalStatus ? '通过' : '拒绝'}！`);
  //     setApprovalModalVisible(false);
  //     setPackageToApprove(null);
  //     approvalForm.resetFields();
  //     fetchFirmwarePackageList();
  //     await fetchFirmwarePackageStatus()
  //   } catch (error) {
  //     message.error('操作失败：' + (error.message || '未知错误'));
  //     console.error('操作失败:', error);
  //   }
  // };

  // 删除升级包
  const handleDeletePackage = async () => {
    if (!packageToDelete) return;

    try {
      await delFirmwarePackage(packageToDelete.packageId);
      message.success("升级包删除成功！");
      setDeleteModalVisible(false);
      setPackageToDelete(null);
      // 刷新列表
      fetchFirmwarePackageList();
    } catch (error) {
      message.error("删除升级包失败：" + (error.message || "未知错误"));
      console.error("删除升级包失败:", error);
    }
  };

  return (
    <div className="upgrade-packages">
      {/* Filter Bar */}
      <Card className="upgrade-packages-filter-bar">
        <div style={{ display: "flex", justifyContent: "space-between" }}>
          <Form
            form={searchForm}
            layout="inline"
            onFinish={handleSearch}
            initialValues={{
              packageType: "",
              status: "",
            }}
          >
            <Form.Item name="packageType" label="升级包类型">
              <Select
                style={{ width: 200 }}
                placeholder="全部类型"
                options={[
                  { value: "", label: "全部类型" },
                  { value: "FIRMWARE", label: "驱鼠器固件" },
                  { value: "SECURITY_PATCH", label: "安全补丁" },
                ]}
              />
            </Form.Item>
            {/* <Form.Item name="status">
            <Select
              style={{ width: 120 }}
              placeholder="全部状态"
              options={[
                { value: '', label: '全部状态' },
                { value: 'PENDING', label: '待审批' },
                { value: 'APPROVED', label: '已发布' },
                { value: 'REJECTED', label: '已拒绝' }
              ]}
            />
          </Form.Item> */}
            <Form.Item name="dateRange" label="升级日期">
              <RangePicker />
            </Form.Item>
            <Form.Item>
              <Space>
                <Button
                  type="primary"
                  htmlType="submit"
                  icon={<SearchOutlined />}
                >
                  搜索
                </Button>
                <Button onClick={handleReset} icon={<ReloadOutlined />}>
                  重置
                </Button>
              </Space>
            </Form.Item>
          </Form>
          <Space>
            {/* <Button
              type='primary'
              icon={<SearchOutlined />}
              onClick={() => setAdvancedSearchVisible(true)}
            >
              高级搜索
            </Button> */}
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setCreateModalVisible(true);
                form.setFieldsValue({
                  packageName: "固件升级",
                  packageType: "FIRMWARE",
                });
              }}
            >
              创建
            </Button>
          </Space>
        </div>
      </Card>

      {/* Statistics */}
      {/* <Row gutter={[16, 16]} className="statistics-cards">
        {statistics.map((stat, index) => (
          <Col span={8} key={index}>
            <Card>
              <Statistic
                title={stat.title}
                value={stat.value}
                prefix={stat.icon}
              />
            </Card>
          </Col>
        ))}
      </Row> */}

      {/* Package List */}
      <Card className="package-list">
        <Table
          loading={loading}
          columns={columns}
          dataSource={firmwarePackageList}
          // rowSelection={{
          //   selectedRowKeys,
          //   onChange: setSelectedRowKeys
          // }}
          scroll={{ x: 1000 }}
          rowKey={"packageId"}
          pagination={{
            total: total,
            pageSize: searchParams.size,
            current: searchParams.page + 1,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
          onChange={handleTableChange}
        />
      </Card>

      {/* Create/Edit Package Modal */}
      <Modal
        title={isEditMode ? "编辑升级包" : "创建升级包"}
        open={createModalVisible}
        onOk={() => form.submit()}
        okText={isEditMode ? "编辑" : "创建"}
        onCancel={() => {
          setCreateModalVisible(false);
          form.resetFields();
          setIsEditMode(false);
          setPackageToEdit(null);
        }}
        width={800}
      >
        <Form form={form} layout="vertical" onFinish={handleCreatePackage}>
          <Form.Item
            name="packageType"
            label="升级包类型"
            rules={[{ required: true, message: "请选择升级包类型" }]}
          >
            <Radio.Group
              onChange={(e) => {
                const type = e.target.value;
                // 根据类型更新包名称
                if (type === "FIRMWARE") {
                  form.setFieldsValue({
                    packageName: "",
                    compatibleDevices: form.getFieldValue("compatibleDevices"),
                  });
                } else if (type === "SECURITY_PATCH") {
                  form.setFieldsValue({
                    packageName: "系统升级包",
                    compatibleDevices: undefined,
                  });
                }
              }}
            >
              <Radio.Button value="SECURITY_PATCH">安全补丁</Radio.Button>
              <Radio.Button value="FIRMWARE">驱鼠器固件</Radio.Button>
            </Radio.Group>
          </Form.Item>

          {/* 使用shouldUpdate条件渲染其他表单项 */}
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.packageType !== currentValues.packageType
            }
          >
            {({ getFieldValue }) => {
              const packageType = getFieldValue("packageType");

              // 如果类型是'FIRMWARE'，则只显示文件上传框
              if (packageType === "FIRMWARE") {
                return (
                  <>
                    {!isEditMode && (
                      <Form.Item
                        name="file"
                        label="升级包文件"
                        rules={[
                          {
                            required: !isEditMode,
                            message: "请上传升级包文件",
                          },
                        ]}
                      >
                        <Upload.Dragger
                          maxCount={1}
                          accept=".qsq"
                          beforeUpload={(file) => {
                            form.setFieldsValue({
                              file: [file],
                            });
                            return false;
                          }}
                          onRemove={() => {
                            form.setFieldsValue({
                              file: undefined,
                            });
                          }}
                        >
                          <p className="ant-upload-drag-icon">
                            <UploadOutlined />
                          </p>
                          <p className="ant-upload-hint">支持.qsq 格式文件</p>
                        </Upload.Dragger>
                      </Form.Item>
                    )}
                    {/* <Form.Item
                      name='compatibleDevices'
                      label='兼容设备型号'
                    >
                      <Select
                        mode="multiple"
                        placeholder="请选择兼容设备型号"
                        allowClear
                        style={{ width: '100%' }}
                        options={deviceTypeDict.map(item => ({
                          label: item.itemName,
                          value: item.itemCode
                        }))}
                      />
                    </Form.Item> */}
                  </>
                );
              }

              // 如果是其他类型，显示所有表单项
              return (
                <>
                  <Row gutter={24}>
                    <Col span={12}>
                      <Form.Item
                        name="packageName"
                        label="升级包名称"
                        rules={[
                          { required: true, message: "请输入升级包名称" },
                        ]}
                      >
                        <Input placeholder="请输入升级包名称" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="version"
                        label="版本号"
                        rules={[{ required: true, message: "请输入版本号" }]}
                      >
                        <Input placeholder="请输入版本号" />
                      </Form.Item>
                    </Col>
                  </Row>
                  {!isEditMode && (
                    <Row>
                      <Col span={24}>
                        <Form.Item
                          name="file"
                          label="升级包文件"
                          rules={[
                            {
                              required: !isEditMode,
                              message: "请上传升级包文件",
                            },
                          ]}
                        >
                          <Upload.Dragger
                            maxCount={1}
                            accept={
                              packageType === "SECURITY_PATCH" ? "*" : ".qsq"
                            }
                            beforeUpload={(file) => {
                              form.setFieldsValue({
                                file: [file],
                              });
                              return false;
                            }}
                            onRemove={() => {
                              form.setFieldsValue({
                                file: undefined,
                              });
                            }}
                          >
                            <p className="ant-upload-drag-icon">
                              <UploadOutlined />
                            </p>
                            <p className="ant-upload-hint">
                              {packageType === "SECURITY_PATCH"
                                ? "支持所有文件格式"
                                : "支持.qsq 格式文件"}
                            </p>
                          </Upload.Dragger>
                        </Form.Item>
                      </Col>
                    </Row>
                  )}

                  <Row gutter={24}>
                    <Col span={12}>
                      <Form.Item name="description" label="升级包描述">
                        <Input.TextArea
                          rows={4}
                          placeholder="请输入升级包描述"
                        />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item name="remark" label="备注">
                        <Input.TextArea
                          rows={4}
                          placeholder="请输入升级包备注"
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                </>
              );
            }}
          </Form.Item>

          <Form.Item name="lockVersion" label="乐观锁" hidden>
            <Input />
          </Form.Item>
        </Form>
      </Modal>

      {/* Approval Modal */}
      {/* <Modal
        title="审批升级包"
        open={approvalModalVisible}
        onOk={() => approvalForm.submit()}
        onCancel={() => {
          setApprovalModalVisible(false);
          setPackageToApprove(null);
          approvalForm.resetFields();
        }}
      >
        <Form
          form={approvalForm}
          layout="vertical"
          onFinish={handleApprovePackage}
        >
          <Form.Item
            name="approvalStatus"
            label="审批结果"
            rules={[{ required: true, message: '请选择审批结果' }]}
          >
            <Radio.Group>
              <Radio.Button value={true}>通过</Radio.Button>
              <Radio.Button value={false}>拒绝</Radio.Button>
            </Radio.Group>
          </Form.Item>
          <Form.Item
            name="approvalComment"
            label="审批意见"
          >
            <Input.TextArea
              rows={4}
              placeholder="请输入审批意见"
            />
          </Form.Item>
        </Form>
      </Modal> */}

      {/* Delete Confirmation Modal */}
      <Modal
        title="确认删除"
        open={deleteModalVisible}
        onOk={handleDeletePackage}
        onCancel={() => {
          setDeleteModalVisible(false);
          setPackageToDelete(null);
        }}
      >
        <p>
          确定要删除升级包 &quot;{packageToDelete?.packageName}&quot;
          吗？此操作不可恢复。
        </p>
      </Modal>

      {/* Package Details Drawer */}
      <Drawer
        title={
          <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
            <EyeOutlined style={{ color: "#1890ff" }} />
            <span>升级包详情</span>
          </div>
        }
        open={detailsModalVisible}
        onClose={() => setDetailsModalVisible(false)}
        width={600}
        placement="right"
        styles={{
          body: {
            padding: "24px",
            backgroundColor: "#fafafa",
          },
        }}
      >
        {upDataPackage && (
          <div
            style={{
              backgroundColor: "#fff",
              borderRadius: "8px",
              padding: "24px",
            }}
          >
            {/* 基本信息 */}
            <div style={{ marginBottom: "24px" }}>
              <h3
                style={{
                  color: "#262626",
                  fontSize: "16px",
                  fontWeight: "600",
                  marginBottom: "16px",
                  borderBottom: "2px solid #1890ff",
                  paddingBottom: "8px",
                }}
              >
                基本信息
              </h3>
              <Descriptions
                column={1}
                size="middle"
                labelStyle={{
                  width: "120px",
                  fontWeight: "500",
                  color: "#595959",
                }}
              >
                <Descriptions.Item label="升级包名称">
                  <span style={{ color: "#262626", fontWeight: "500" }}>
                    {upDataPackage.packageName}
                  </span>
                </Descriptions.Item>
                <Descriptions.Item label="版本号">
                  <Tag color="blue" style={{ borderRadius: "4px" }}>
                    {upDataPackage.version}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="类型">
                  <Tag
                    color={
                      upDataPackage.packageType === "FIRMWARE"
                        ? "green"
                        : "orange"
                    }
                    style={{ borderRadius: "4px" }}
                  >
                    {upDataPackage.packageType === "FIRMWARE"
                      ? "驱鼠器固件"
                      : "安全补丁"}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="文件大小">
                  <span style={{ color: "#262626" }}>
                    {`${(upDataPackage.fileSize / 1024 / 1024).toFixed(2)} MB`}
                  </span>
                </Descriptions.Item>
                <Descriptions.Item label="兼容设备型号">
                  <span style={{ color: "#262626" }}>
                    {upDataPackage.compatibleDevices}
                  </span>
                </Descriptions.Item>
              </Descriptions>
            </div>

            <Divider style={{ margin: "24px 0" }} />

            {/* 详细描述 */}
            <div style={{ marginBottom: "24px" }}>
              <h3
                style={{
                  color: "#262626",
                  fontSize: "16px",
                  fontWeight: "600",
                  marginBottom: "16px",
                  borderBottom: "2px solid #52c41a",
                  paddingBottom: "8px",
                }}
              >
                详细描述
              </h3>
              <div
                style={{
                  backgroundColor: "#f6ffed",
                  border: "1px solid #b7eb8f",
                  borderRadius: "6px",
                  padding: "16px",
                  marginBottom: "16px",
                }}
              >
                <p
                  style={{
                    margin: 0,
                    color: "#262626",
                    lineHeight: "1.6",
                  }}
                >
                  {upDataPackage.description || "暂无描述"}
                </p>
              </div>

              {upDataPackage.remark && (
                <div
                  style={{
                    backgroundColor: "#fff7e6",
                    border: "1px solid #ffd591",
                    borderRadius: "6px",
                    padding: "16px",
                  }}
                >
                  <h4
                    style={{
                      color: "#d46b08",
                      fontSize: "14px",
                      fontWeight: "500",
                      marginBottom: "8px",
                    }}
                  >
                    备注信息
                  </h4>
                  <p
                    style={{
                      margin: 0,
                      color: "#262626",
                      lineHeight: "1.6",
                    }}
                  >
                    {upDataPackage.remark}
                  </p>
                </div>
              )}
            </div>

            <Divider style={{ margin: "24px 0" }} />

            {/* 创建信息 */}
            <div>
              <h3
                style={{
                  color: "#262626",
                  fontSize: "16px",
                  fontWeight: "600",
                  marginBottom: "16px",
                  borderBottom: "2px solid #722ed1",
                  paddingBottom: "8px",
                }}
              >
                创建信息
              </h3>
              <Descriptions
                column={1}
                size="middle"
                labelStyle={{
                  width: "120px",
                  fontWeight: "500",
                  color: "#595959",
                }}
              >
                <Descriptions.Item label="上传时间">
                  <span style={{ color: "#262626" }}>
                    {upDataPackage.createdAt}
                  </span>
                </Descriptions.Item>
                <Descriptions.Item label="上传人">
                  <span style={{ color: "#262626" }}>
                    {upDataPackage.createdBy}
                  </span>
                </Descriptions.Item>
              </Descriptions>
            </div>
          </div>
        )}
      </Drawer>

      {/* Add Advanced Search Modal */}
      {advancedSearchVisible && (
        <AdvancedSearch
          advancedSearchVisible={advancedSearchVisible}
          onCancel={() => {
            setAdvancedSearchVisible(false);
          }}
          data={[
            {
              label: "升级包名称",
              value: "packageName",
            },
            {
              label: "版本号",
              value: "version",
            },
            {
              label: "类型",
              value: "packageType",
              options: [
                { label: "驱鼠器固件", value: "FIRMWARE" },
                { label: "安全补丁", value: "SECURITY_PATCH" },
              ],
              children: (setInputValue) => (
                <Select
                  placeholder="请选择升级包类型"
                  style={{ width: "300px" }}
                  onChange={(value) => {
                    setInputValue(value);
                  }}
                  options={[
                    { label: "驱鼠器固件", value: "FIRMWARE" },
                    { label: "安全补丁", value: "SECURITY_PATCH" },
                  ]}
                />
              ),
            },
            {
              label: "文件大小",
              value: "fileSize",
            },
            {
              label: "兼容设备型号",
              value: "compatibleDevices",
            },
            {
              label: "升级包描述",
              value: "description",
            },
            {
              label: "备注",
              value: "remark",
            },
          ]}
          getSearchData={(data) => {
            console.log(data);
            // TODO: Implement advanced search logic
            setAdvancedSearchVisible(false);
          }}
        />
      )}
    </div>
  );
};

export default UpgradePackages;
