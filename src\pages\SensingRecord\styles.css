.sensing-record {
  padding: 24px;
  height:91vh;
  display: flex;
  flex-direction: column;
  background: #f0f2f5;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  /* margin-bottom: 24px; */
}

.statistics-cards {
  margin-bottom: 24px;
}

.statistics-cards .ant-card {
  border-radius: 8px;
}

.trend-info {
  margin-top: 8px;
  font-size: 14px;
}

.trend-up {
  color: #52c41a;
}

.trend-down {
  color: #ff4d4f;
}

.trend-label {
  margin-left: 8px;
  color: rgba(0, 0, 0, 0.45);
}

.sensing-record-content {
  flex: 1;
  background: transparent;
}

.main-content {
  flex: 1;
  background: transparent;
  height: calc(100vh - 120px);
}

.group-sider {
  background: #fff;
  border-right: 1px solid #f0f0f0;
  margin-right: 24px;
  height: calc(100vh - 120px); /* 确保侧边栏有足够的高度 */
  border-radius: 8px;
}

.group-header {
  padding: 16px 16px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.group-tree-scroll {
  height: calc(100% - 150px);
  overflow-y: auto;
  padding: 0 16px;
}

/* 树组件样式优化 */
.group-tree-scroll .ant-tree {
  background: transparent;
  font-size: 14px;
  color: #333;
}

.group-tree-scroll .ant-tree .ant-tree-list-holder-inner {
  align-items: stretch;
}

.group-tree-scroll .ant-tree .ant-tree-treenode {
  padding: 0;
  width: 100%;
  margin: 0;
}

.group-tree-scroll .ant-tree .ant-tree-node-content-wrapper {
  padding: 6px 8px;
  border-radius: 4px;
  transition: all 0.2s;
  line-height: 20px;
  height: 32px;
  display: flex;
  align-items: center;
  width: calc(100% - 24px);
  margin-left: 0;
}

.group-tree-scroll .ant-tree .ant-tree-node-content-wrapper:hover {
  background-color: #f0f0f0;
}

.group-tree-scroll .ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected {
  background-color: #e6f7ff;
  color: #1890ff;
}

.group-tree-scroll .ant-tree .ant-tree-title {
  display: inline-block;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 14px;
  color: inherit;
}

.group-tree-scroll .ant-tree .ant-tree-switcher {
  width: 20px;
  height: 32px;
  line-height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 4px;
}

.group-tree-scroll .ant-tree .ant-tree-iconEle {
  width: 16px;
  height: 16px;
  line-height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 6px;
}

/* 树节点缩进样式 */
.group-tree-scroll .ant-tree .ant-tree-indent-unit {
  width: 20px;
}

/* 树节点层级样式 */
.group-tree-scroll .ant-tree .ant-tree-treenode-leaf-last > .ant-tree-node-content-wrapper,
.group-tree-scroll .ant-tree .ant-tree-treenode-leaf-last > .ant-tree-switcher {
  border-bottom: none;
}

/* 确保树节点正确对齐 */
.group-tree-scroll .ant-tree .ant-tree-child-tree {
  display: block;
}

.group-tree-scroll .ant-tree .ant-tree-child-tree .ant-tree-treenode {
  padding-left: 0;
}

.tree-search {
  margin: 16px 0;
}

.record-content {
  background: transparent !important;
  padding: 0 !important;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-bar {
  background: #fff;
  border-radius: 4px;
}

.record-table {
  flex: 1;
  background: #fff;
  border-radius: 8px;
  height: 100%;
  padding: 16px;
  overflow-y: auto;
}

.event-details .ant-descriptions {
  margin-bottom: 24px;
}

/* Responsive styles */
@media (max-width: 1200px) {
  .statistics-cards .ant-col {
    flex: 0 0 50%;
    max-width: 50%;
  }
}

@media (max-width: 768px) {
  .sensing-record {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
  }

  .header-right {
    width: 100%;
  }

  .statistics-cards .ant-col {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .main-content {
    flex-direction: column;
  }

  .group-sider {
    width: 100% !important;
    max-width: 100% !important;
    margin-right: 0;
    margin-bottom: 24px;
  }
}