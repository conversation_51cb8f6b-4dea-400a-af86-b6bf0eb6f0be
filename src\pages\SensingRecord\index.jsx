import { useState, useEffect } from 'react'
import {
  Layout,
  Card,
  Tabs,
  Input,
  Button,
  Space,
  Table,
  Tag,
  DatePicker,
  Select,
  Tree,
  Badge,
  Form,
  Drawer,
  AutoComplete
} from 'antd'
import {
  DownloadOutlined,
  GlobalOutlined,
  TeamOutlined
} from '@ant-design/icons'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import {
  getDeviceGroupsByLocation,
  getCustomerGroupsTree
} from '../../service/deviceGroups'
import {
  getDeviceLikeDeviceNo
} from "../../service/deviceRepellent";
import { getSensingRecordList, getDeviceRatEvents } from '../../service/sensingRecord'
import 'dayjs/locale/zh-cn'
import './styles.css'
import useSelect from '../../hooks/useSelect'
import { useNavigate } from 'react-router-dom'
import { getevent } from '../../utils/loginLogger'
import { color } from 'echarts/core'
import { render } from 'react-dom'
import { debounce} from "lodash";
dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

const { Sider, Content } = Layout
const { Search } = Input
const { RangePicker } = DatePicker

const SensingRecord = () => {
  const [activeTab, setActiveTab] = useState('location') // 当前选中的tab
  const [selectedKeys, setSelectedKeys] = useState([]) // 选中的节点
  const [expandedKeys, setExpandedKeys] = useState([]) // 展开的节点
  const [treeData, setTreeData] = useState([]) // 树数据
  const [searchParams, setSearchParams] = useState({
    page: 0,
    size: 10,
    groupType: 0, // 0是地理分组，1是客户分组
    groupId: '', // 地理分组
    orgId: '', // 客户分组
    type: '', // 0 手动触发 1 自动触发
    status: '', // 状态 0 待执行 1 执行中 2 已完成 3 已失败
    startTime: dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'), // 默认今天开始时间
    endTime: dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss'), // 默认今天结束时间
    isNotGroup: false
  })
  const [total, setTotal] = useState(0) // 总条数
  const [searchForm] = Form.useForm() // 搜索表单实例
  const [timeRange, setTimeRange] = useState('today')
  const [dateRangeValue, setDateRangeValue] = useState([
    dayjs().startOf('day'),
    dayjs().endOf('day')
  ]) // RangePicker的值
  const [records, setRecords] = useState([])
  const [
    node,
    searchValue,
    autoExpandParent,
    filteredTreeData,
    setAutoExpandParent
  ] = useSelect(treeData, setExpandedKeys)
  const navigate = useNavigate()
  const [detailDrawerVisible, setDetailDrawerVisible] = useState(false)
  const [detailData, setDetailData] = useState(null)
  const [loading,setLoading] = useState(false)

  // 获取位置树
  const getLocationTree = async () => {
    setLoading(true)
    try {
      const res = await getDeviceGroupsByLocation(1)

      const key = []
      const transformNode = (item, isFlag = false) => {
        const nodeKey = item.id || item.locationId

        // 如果没有有效的key，跳过这个节点
        if (!nodeKey) {
          return null
        }

        key.push(nodeKey)

        // 处理子节点
        let children = undefined
        if (item.children && Array.isArray(item.children) && item.children.length > 0) {
          children = item.children.map(child => transformNode(child, isFlag)).filter(Boolean)
        } else if (item.deviceLocationTreeVos && Array.isArray(item.deviceLocationTreeVos) && item.deviceLocationTreeVos.length > 0) {
          children = item.deviceLocationTreeVos.map(child => transformNode(child, isFlag)).filter(Boolean)
        }

        // 如果children为空数组，设置为undefined
        if (children && children.length === 0) {
          children = undefined
        }

        const transformedNode = {
          title: item.groupName || item.description || `节点-${nodeKey}`,
          key: String(nodeKey), // 确保key是字符串
          value: nodeKey,
          isLocal: item.isLocal,
          ...(isFlag && { selectable: !!item.isLocal }),
          ...(children && { children })
        }

        return transformedNode
      }

      const transformedData = res.map(item => transformNode(item)).filter(Boolean)

      const finalTreeData = [
        ...transformedData,
        { title: '未分组', key: 'isNotGroup', isLocal: 0 }
      ]

      setTreeData(finalTreeData)
      setExpandedKeys(key) // 默认展开
      setSelectedKeys([key[0]]) //
      const tempObj = {
        groupId: key[0],
        groupType: 0,
        orgId: '',
        page: 0
      }
      updateSeachParam(tempObj)
      fetchRecordList(tempObj)
      setLoading(false)
    } catch (error) {
      console.error('Failed to fetch location tree:', error)
    }
  }

  // 获取客户分组树
  const fetchCustomerGroupsTree = async () => {
    setLoading(true)
    try {
      const res = await getCustomerGroupsTree()
      const transformNode = item => ({
        title: item.ownershipName,
        key: item.ownershipId,
        children:
          item.children && item.children.length > 0
            ? item.children.map(child => transformNode(child))
            : undefined
      })
      const transformedData = res.map(item => transformNode(item))
      setTreeData([
        ...transformedData,
        { title: '未分组', key: 'isNotGroup', isLocal: 0 }
      ])
      setSelectedKeys([transformedData[0]?.key || 'isNotGroup']) // 设置当前选中的
      const tempObj = {
        page: 0,
        orgId: transformedData[0]?.key || 'isNotGroup',
        groupType: '',
        groupId: ''
      }
      updateSeachParam(tempObj)
      fetchRecordList(tempObj)
      setLoading(false)
    } catch (error) {
      console.error('Failed to fetch customer groups tree:', error)
    }
  }

  // 切换tab
  const onGroupTypeChange = key => {
    if (window.cancelToken) {
      window.cancelToken() // 取消请求
    }

    setActiveTab(key)
    // 先设置为空数组而不是null，避免渲染问题
    setTreeData([])
    if (key === 'location') {
      getLocationTree()
    } else {
      fetchCustomerGroupsTree()
    }
  }

  // 获取驱鼠记录列表
  const fetchRecordList = async (obj = {}) => {
    setLoading(true)
    try {
      const params = {
        ...searchParams,
        ...obj
      }
      const res = await getSensingRecordList(params)
      setRecords(res.content || [])
      setTotal(res.totalElements || 0)
      setLoading(false)
    } catch (error) {
      console.error('Failed to fetch record list:', error)
    }
  }

  // 树节点选中
  const onTreeSelect = (selectedKeys, node) => {
    if (selectedKeys.length > 0) {
      setSelectedKeys(selectedKeys)
      const tempObj = {
        [activeTab === 'location' ? 'groupId' : 'orgId']:
          selectedKeys[0] !== 'isNotGroup' ? selectedKeys[0] : '',
        groupType: activeTab === 'location' ? node.node.isLocal : '',
        isNotGroup: node.node.key === 'isNotGroup',
        page: 0,
        ...searchForm.getFieldsValue()
      }
      updateSeachParam(tempObj)
      fetchRecordList(tempObj)
    }
  }

  // 处理分页变化
  const handleTableChange = pagination => {
    const tempObj = {
      page: pagination.current - 1,
      size: pagination.pageSize
    }
    updateSeachParam(tempObj)
    fetchRecordList(tempObj)
  }

  // 处理时间范围变化
  const handleTimeRangeChange = value => {
    setTimeRange(value)
    let startTime, endTime
    switch (value) {
      case 'today':
        startTime = dayjs().startOf('day')
        endTime = dayjs().endOf('day')
        break
      case 'yesterday':
        startTime = dayjs().subtract(1, 'day').startOf('day')
        endTime = dayjs().subtract(1, 'day').endOf('day')
        break
      case 'week':
        startTime = dayjs().startOf('week')
        endTime = dayjs().endOf('week')
        break
      case 'month':
        startTime = dayjs().startOf('month')
        endTime = dayjs().endOf('month')
        break
      case 'custom':
        // 自定义时设置为今天的起始时间作为默认值
        startTime = dayjs().startOf('day')
        endTime = dayjs().endOf('day')
        setDateRangeValue([startTime, endTime])
        return // 不自动更新搜索，等待用户选择
      default:
        return
    }

    // 更新RangePicker的值
    setDateRangeValue([startTime, endTime])

    // 更新搜索参数并执行搜索
    updateSeachParam({
      startTime: startTime.format('YYYY-MM-DD HH:mm:ss'),
      endTime: endTime.format('YYYY-MM-DD HH:mm:ss')
    })
    fetchRecordList({
      startTime: startTime.format('YYYY-MM-DD HH:mm:ss'),
      endTime: endTime.format('YYYY-MM-DD HH:mm:ss')
    })
  }

  // 处理自定义时间范围变化
  const handleCustomTimeRangeChange = dates => {
    if (dates) {
      setDateRangeValue(dates)
      updateSeachParam({
        startTime: dates[0].format('YYYY-MM-DD HH:mm:ss'),
        endTime: dates[1].format('YYYY-MM-DD HH:mm:ss')
      })
      fetchRecordList({
        startTime: dates[0].format('YYYY-MM-DD HH:mm:ss'),
        endTime: dates[1].format('YYYY-MM-DD HH:mm:ss')
      })
    }
  }

  // 处理搜索
  const handleSearch = async values => {
    const tempObj = {
      page: 0,
      ...values
    }
    updateSeachParam(tempObj)
    await fetchRecordList(tempObj)
  }

  // 处理重置
  const handleReset = () => {
    searchForm.resetFields()
    const tempObj = {
      page: 0,
      size: 10,
      type: '',
      status: ''
    }
    updateSeachParam(tempObj)
    setTimeRange('today')
    setDateRangeValue([
      dayjs().startOf('day'),
      dayjs().endOf('day')
    ])
    fetchRecordList(tempObj)
  }

  //更新搜索条件
  const updateSeachParam = obj => {
    setSearchParams(pre => ({
      ...pre,
      ...obj
    }))
  }

    // 处理驱鼠器编号搜索
  const handleDeviceNoSearch = debounce(async (value) => {
    if (value) {
      try {
        const response = await getDeviceLikeDeviceNo(value);
        console.log("response", response);
        if (response) {
          // 根据接口返回的实际数据结构进行处理
          // 接口返回的是字符串数组 ["TEST100", "TEST201", ...]
          setAutocompleteOptions(
            response.map((deviceNo) => ({
              value: deviceNo,
            }))
          );
        }
      } catch (error) {
        console.error("Failed to fetch device numbers:", error);
      }
    } else {
      setAutocompleteOptions([]);
    }
  }, 500);

  const getDevice = async (id) => {
    try {
      const res = await getDeviceRatEvents(id)
      setDetailData(res)
      setDetailDrawerVisible(true)
    } catch (error) {
      console.error('获取驱鼠器详情失败:', error)
    }
  }

  useEffect(() => {
    getLocationTree()
    // 清理函数，在组件卸载时取消请求
    return () => {
      if (window.cancelToken) {
        window.cancelToken() // 取消请求
      }
    }
  }, [])

  const renderWorkMode = (mode) => {
    if (!mode) return '-'
    switch (mode) {
      case 'trigger':
        return '触发'
      case 'always':
        return '持续'
      case 'switch':
        return '开关'
      default:
        return mode
    }
  }

  // Table columns
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      ellipsis: true
    },
    
    {
      title: '鼠患事件ID',
      width: '20%',
      dataIndex: 'ratEventId',
      key: 'ratEventId',
      ellipsis: true,
      render: (ratEventId) => ratEventId ? (
        <Button
          type="link"
          onClick={() => {
            // 保存鼠患事件ID，然后跳转
            getevent(ratEventId);
            navigate('/sensing/event', { state: { ratEventId } });
          }}
        >
          {ratEventId}
        </Button>
      ) : '-'
    },
    {
      title: '驱鼠器编号',
      dataIndex: 'deviceNo',
      key: 'deviceNo',
      ellipsis: true
    },
    {
      title: '驱鼠器名',
      dataIndex: 'deviceName',
      key: 'deviceName',
      ellipsis: true
    },
    {
      title: '策略名称',
      dataIndex: 'customModelId',
      key: 'customModelId',
      ellipsis: true,
      render:(text, record) => {
        <a onClick={()=>{navigate('/strategy/config')}}>
          {text || '-'}
        </a>
      }
    },
    {
      title: '执行状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={status === 0 ? 'processing' : status === 1 ? 'success' : 'error'}>
          {status === 0 ? '待执行' : status === 1 ? '已执行成功' : '执行失败'}
        </Tag>
      )
    },

    {
      title: '触发类型',
      dataIndex: 'type',
      key: 'type',
      ellipsis: true,
      render: type => {
        const typeText = type === 0 ? '手动' : type === 1 ? '自动' : '未知'
        return (
          <Tag color={type === 0 ? 'green' : type === 1 ? 'blue' : 'default'}>
            {typeText}
          </Tag>
        )
      }
    },
    {
      title: '执行时间',
      dataIndex: 'runTime',
      key: 'runTime',
      ellipsis: true
      // render: time => <Tooltip title={time}>{dayjs(time).fromNow()}</Tooltip>
    },
    // {
    //   title: '结束时间',
    //   dataIndex: 'endTime',
    //   key: 'endTime',
    //   ellipsis: true
    //   // render: time => time ? <Tooltip title={time}>{dayjs(time).fromNow()}</Tooltip> : '-'
    // },
    // {
    //   title: '状态',
    //   dataIndex: 'status',
    //   key: 'status',
    //   ellipsis: true,
    //   render: status => {
    //     let statusConfig = {
    //       0: { text: '待执行', status: 'warning' },
    //       1: { text: '执行中', status: 'processing' },
    //       2: { text: '已完成', status: 'success' },
    //       3: { text: '失败', status: 'error' }
    //     }
    //     const config = statusConfig[status] || { text: '未知', status: 'default' }
    //     return <Badge status={config.status} text={config.text} />
    //   }
    // },
    {
      title: '操作',
      key: 'action',
      render: (text, record) => {
        return (
          <Space>
            <Button type='link' onClick={() => getDevice(record.id)}>详情</Button>
          </Space>
        )
      }
    }
  ]

  return (
    <div className='sensing-record'>
      {/* Page Header */}
      <div className='page-header'>
        <div className='header-left'>{/* <h2>驱鼠记录</h2> */}</div>
      </div>

      {/* Main Content */}
      <Layout className='main-content' style={{backgroundColor: '#fff'}}>
        {/* Left Sider */}
        <Sider width={300} className='group-sider'>
          <div className="group-header">
            <Tabs
              activeKey={activeTab}
              onChange={onGroupTypeChange}
              items={[
                {
                  key: 'location',
                  label: (
                    <span>
                      <GlobalOutlined />
                      地理分组
                    </span>
                  )
                },
                {
                  key: 'customer',
                  label: (
                    <span>
                      <TeamOutlined />
                      客户分组
                    </span>
                  )
                }
              ]}
            />
          </div>
          {node()}
          <div className="group-tree-scroll">
            {treeData && treeData.length > 0 ? (
              <Tree
                loading = {loading}
                showIcon={false}
                showLine={false}
                onExpand={(keys) => {
                  setExpandedKeys(keys);
                  setAutoExpandParent(false); // 用户手动展开后关闭自动展开
                }}
                autoExpandParent={autoExpandParent}
                onSelect={onTreeSelect}
                selectedKeys={selectedKeys}
                expandedKeys={expandedKeys}
                treeData={searchValue ? filteredTreeData : treeData}
                blockNode
              />
            ) : (
              <div style={{ padding: '20px', textAlign: 'center', color: '#999' }}>
                {!treeData ? '加载中...' : '暂无数据'}
              </div>
            )}
          </div>
        </Sider>

        <Content className='record-content'>
          <Card>
            <div className='search-filter'>
              <Space size='middle' className='search-left'>
                <Form form={searchForm} layout='inline' onFinish={handleSearch}>
                  <Form.Item name='type' label='触发类型'>
                    <Select
                      placeholder='请选择触发类型'
                      allowClear
                      style={{ width: 200 }}
                      options={[
                        { value: '0', label: '手动触发' },
                        { value: '1', label: '自动触发' }
                      ]}
                    />
                  </Form.Item>
                  <Form.Item name='deviceNo' label='驱鼠器编号'>
                    <Input placeholder='请输入驱鼠器编号' style={{ width: 200 }} />
                  </Form.Item>
                  <Form.Item name='ratEventId' label='鼠患事件ID'>
                    <Input placeholder='请输入鼠患事件ID' style={{ width: 200 }} />
                  </Form.Item>
                  {/* <Form.Item name='status'>
                <Select
                  placeholder='请选择执行状态'
                  allowClear
                  style={{ width: 200 }}
                  options={[
                    { value: '0', label: '待执行' },
                    { value: '1', label: '执行中' },
                    { value: '2', label: '已完成' },
                    { value: '3', label: '失败' }
                  ]}
                />
              </Form.Item> */}

                  <Form.Item>
                    <Space>
                      <Button type='primary' htmlType='submit'>
                        搜索
                      </Button>
                      <Button onClick={handleReset}>重置</Button>
                    </Space>
                  </Form.Item>
                </Form>
              </Space>
              <Space size='middle' className='search-right'>
                <Select
                  value={timeRange}
                  style={{ width: 120 }}
                  options={[
                    { value: 'today', label: '今天' },
                    { value: 'yesterday', label: '昨天' },
                    { value: 'week', label: '本周' },
                    { value: 'month', label: '本月' },
                    { value: 'custom', label: '自定义' }
                  ]}
                  onChange={handleTimeRangeChange}
                />
                  <RangePicker
                    value={dateRangeValue}
                    onChange={handleCustomTimeRangeChange}
                    showTime
                    format='YYYY-MM-DD HH:mm:ss'
                    disabled={timeRange !== 'custom'}
                  />
                {/* <Button icon={<DownloadOutlined />}>导出</Button> */}
                {/* <Button
              danger
              icon={<DeleteOutlined />}
              onClick={handleBatchDelete}
              disabled={selectedRowKeys.length === 0}
            >
              批量删除
            </Button> */}
              </Space>
            </div>
          </Card>

          <div className='record-table'>
            <Table
              rowKey="id"
              loading={loading}
              columns={columns}
              dataSource={records}
              pagination={{
                total,
                pageSize: searchParams.size,
                current: searchParams.page + 1,
                showQuickJumper: true,
                showSizeChanger: true,
                showTotal: total => `共 ${total} 条记录`
              }}
              onChange={handleTableChange}
            />
          </div>

        </Content>
      </Layout>

      {/* Record Detail Drawer */}
      <Drawer
        title="驱鼠记录详情"
        open={detailDrawerVisible}
        onClose={() => {
          setDetailDrawerVisible(false)
          setDetailData(null)
        }}
        width={400}
      >
        {detailData && (
          <div style={{ display: 'flex', gap: '24px' }}>
            <div style={{ flex: 1 }}>
              <p>
                <strong>设备ID：</strong>
                {detailData.deviceId || '-'}
              </p>
              <p>
                <strong>鼠患事件ID：</strong>
                {detailData.ratEventId || '-'}
              </p>
              <p>
                <strong>策略ID：</strong>
                {detailData.customModelId || '-'}
              </p>
              <p>
                <strong>设备编号：</strong>
                {detailData.deviceNo || '-'}
              </p>
              <p>
                <strong>摄像头编号：</strong>
                {detailData.cameraNo || '-'}
              </p>
              <p>
                <strong>摄像头名称：</strong>
                {detailData.cameraName || '-'}
              </p>
              <p>
                <strong>执行状态：</strong>
                {detailData.status === 0 ? '未执行' : detailData.status === 1 ? '已执行成功' : detailData.status === 3 ? '执行失败' : '-'}
              </p>
              <p>
                <strong>触发类型：</strong>
                {detailData.type === 0 ? '手动' : detailData.type === 1 ? '自动' : '-'}
              </p>
              <p>
                <strong>设备名称：</strong>
                {detailData.deviceName || '-'}
              </p>
              <p>
                <strong>策略名称：</strong>
                {detailData.configName || '-'}
              </p>
              <p>
                <strong>超声波工作状态：</strong>
                {renderWorkMode(detailData.ulWorkMode)}
              </p>
              <p>
                <strong>灯光工作模块：</strong>
                {renderWorkMode(detailData.ledWorkMode)}
              </p>
              <p>
                <strong>喇叭工作模块：</strong>
                {renderWorkMode(detailData.soundWorkMode)}
              </p>
              <p>
                <strong>执行时间：</strong>
                {detailData.endTime || '-'}
              </p>
            </div>
          </div>
        )}
      </Drawer>
    </div>
  )
}

export default SensingRecord
